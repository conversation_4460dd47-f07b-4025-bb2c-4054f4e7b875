@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Enhanced warm color palette inspired by bookstore aesthetics */
  --background: oklch(0.98 0.008 85);
  --foreground: oklch(0.25 0.02 45);
  --card: oklch(0.995 0.005 85);
  --card-foreground: oklch(0.25 0.02 45);
  --popover: oklch(0.995 0.005 85);
  --popover-foreground: oklch(0.25 0.02 45);

  /* Warm brown primary inspired by leather-bound books */
  --primary: oklch(0.45 0.08 45);
  --primary-foreground: oklch(0.98 0.008 85);

  /* Soft cream secondary */
  --secondary: oklch(0.94 0.015 75);
  --secondary-foreground: oklch(0.35 0.03 45);

  /* Muted warm tones */
  --muted: oklch(0.92 0.012 75);
  --muted-foreground: oklch(0.55 0.025 45);

  /* Accent with golden undertones */
  --accent: oklch(0.88 0.025 65);
  --accent-foreground: oklch(0.35 0.03 45);

  --destructive: oklch(0.577 0.245 27.325);

  /* Subtle warm borders */
  --border: oklch(0.88 0.015 75);
  --input: oklch(0.92 0.012 75);
  --ring: oklch(0.55 0.05 45);

  /* Enhanced chart colors with warm tones */
  --chart-1: oklch(0.65 0.15 45);
  --chart-2: oklch(0.6 0.12 85);
  --chart-3: oklch(0.55 0.08 25);
  --chart-4: oklch(0.7 0.18 65);
  --chart-5: oklch(0.75 0.15 105);

  /* Sidebar with warm tones */
  --sidebar: oklch(0.96 0.01 75);
  --sidebar-foreground: oklch(0.25 0.02 45);
  --sidebar-primary: oklch(0.45 0.08 45);
  --sidebar-primary-foreground: oklch(0.98 0.008 85);
  --sidebar-accent: oklch(0.88 0.025 65);
  --sidebar-accent-foreground: oklch(0.35 0.03 45);
  --sidebar-border: oklch(0.88 0.015 75);
  --sidebar-ring: oklch(0.55 0.05 45);

  /* Custom gradient variables for enhanced design */
  --gradient-warm: linear-gradient(135deg, oklch(0.98 0.008 85) 0%, oklch(0.94 0.015 75) 100%);
  --gradient-primary: linear-gradient(135deg, oklch(0.45 0.08 45) 0%, oklch(0.55 0.06 35) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.88 0.025 65) 0%, oklch(0.82 0.035 55) 100%);

  /* Glassmorphism variables */
  --glass-bg: oklch(0.98 0.008 85 / 0.8);
  --glass-border: oklch(0.88 0.015 75 / 0.3);
  --glass-shadow: 0 8px 32px oklch(0.25 0.02 45 / 0.1);
}

.dark {
  /* Dark mode with warm undertones */
  --background: oklch(0.15 0.015 45);
  --foreground: oklch(0.95 0.008 75);
  --card: oklch(0.18 0.02 45);
  --card-foreground: oklch(0.95 0.008 75);
  --popover: oklch(0.18 0.02 45);
  --popover-foreground: oklch(0.95 0.008 75);

  /* Lighter warm primary for dark mode */
  --primary: oklch(0.75 0.08 55);
  --primary-foreground: oklch(0.15 0.015 45);

  /* Dark mode secondary with warm tones */
  --secondary: oklch(0.25 0.025 45);
  --secondary-foreground: oklch(0.95 0.008 75);

  /* Dark muted colors */
  --muted: oklch(0.25 0.025 45);
  --muted-foreground: oklch(0.7 0.015 65);

  /* Dark accent */
  --accent: oklch(0.35 0.035 55);
  --accent-foreground: oklch(0.95 0.008 75);

  --destructive: oklch(0.704 0.191 22.216);

  /* Dark borders with warm undertones */
  --border: oklch(0.95 0.008 75 / 0.15);
  --input: oklch(0.95 0.008 75 / 0.2);
  --ring: oklch(0.7 0.05 55);

  /* Dark mode chart colors */
  --chart-1: oklch(0.65 0.15 45);
  --chart-2: oklch(0.6 0.12 85);
  --chart-3: oklch(0.75 0.15 105);
  --chart-4: oklch(0.7 0.18 65);
  --chart-5: oklch(0.68 0.16 25);

  /* Dark sidebar */
  --sidebar: oklch(0.18 0.02 45);
  --sidebar-foreground: oklch(0.95 0.008 75);
  --sidebar-primary: oklch(0.75 0.08 55);
  --sidebar-primary-foreground: oklch(0.15 0.015 45);
  --sidebar-accent: oklch(0.35 0.035 55);
  --sidebar-accent-foreground: oklch(0.95 0.008 75);
  --sidebar-border: oklch(0.95 0.008 75 / 0.15);
  --sidebar-ring: oklch(0.7 0.05 55);

  /* Dark mode gradients */
  --gradient-warm: linear-gradient(135deg, oklch(0.15 0.015 45) 0%, oklch(0.25 0.025 45) 100%);
  --gradient-primary: linear-gradient(135deg, oklch(0.75 0.08 55) 0%, oklch(0.65 0.06 45) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.35 0.035 55) 0%, oklch(0.45 0.045 65) 100%);

  /* Dark glassmorphism */
  --glass-bg: oklch(0.15 0.015 45 / 0.8);
  --glass-border: oklch(0.95 0.008 75 / 0.2);
  --glass-shadow: 0 8px 32px oklch(0.05 0.005 45 / 0.3);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  
  /* Skip link styles */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary);
    color: var(--primary-foreground);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
    transition: top 0.3s;
  }
  
  .skip-link:focus {
    top: 6px;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    * {
      border-color: ButtonText !important;
    }
    
    .bg-primary {
      background-color: ButtonText !important;
      color: ButtonFace !important;
    }
    
    .text-muted-foreground {
      color: ButtonText !important;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
  
  /* Focus visible improvements */
  *:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }
  
  /* Ensure minimum color contrast */
  .text-muted-foreground {
    color: var(--muted-foreground);
    /* Ensure at least 4.5:1 contrast ratio */
  }
}

/* Enhanced responsive design utilities */
@layer utilities {
  /* Touch-friendly interactive elements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Mobile-optimized text sizes */
  .text-mobile-xs { font-size: 0.75rem; line-height: 1rem; }
  .text-mobile-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .text-mobile-base { font-size: 1rem; line-height: 1.5rem; }
  .text-mobile-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .text-mobile-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .text-mobile-2xl { font-size: 1.5rem; line-height: 2rem; }
  .text-mobile-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  
  /* Responsive spacing utilities */
  .space-mobile-tight > * + * { margin-top: 0.5rem; }
  .space-mobile-normal > * + * { margin-top: 1rem; }
  .space-mobile-loose > * + * { margin-top: 1.5rem; }
  
  /* Mobile-first container with better padding */
  .container-mobile {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }
  
  @media (min-width: 640px) {
    .container-mobile {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .container-mobile {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  /* Responsive grid utilities - Mobile optimized to show 2-4 cards */
  .grid-responsive-books {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
  }

  @media (min-width: 480px) {
    .grid-responsive-books {
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 1rem;
    }
  }

  @media (min-width: 640px) {
    .grid-responsive-books {
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 1.25rem;
    }
  }

  @media (min-width: 768px) {
    .grid-responsive-books {
      grid-template-columns: repeat(4, minmax(0, 1fr));
      gap: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive-books {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 1280px) {
    .grid-responsive-books {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  
  .grid-responsive-bundles {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
  }

  @media (min-width: 480px) {
    .grid-responsive-bundles {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 1rem;
    }
  }

  @media (min-width: 640px) {
    .grid-responsive-bundles {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 1.25rem;
    }
  }

  @media (min-width: 768px) {
    .grid-responsive-bundles {
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive-bundles {
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 1.5rem;
    }
  }
  
  .grid-responsive-features {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1.5rem;
  }
  
  @media (min-width: 768px) {
    .grid-responsive-features {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  @media (min-width: 1024px) {
    .grid-responsive-features {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  
  /* Mobile reading optimizations */
  .reading-container {
    max-width: none;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
  
  @media (min-width: 640px) {
    .reading-container {
      max-width: 56rem;
      margin-left: auto;
      margin-right: auto;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .reading-container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  /* Mobile form improvements */
  .form-mobile {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }
  
  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    font-size: 1rem; /* Prevents zoom on iOS */
  }
  
  /* Safe area handling for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Mobile navigation improvements */
  .mobile-nav-item {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  
  /* Responsive image containers */
  .image-responsive {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
  }
  
  .image-responsive img {
    object-fit: cover;
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }
  
  .image-responsive:hover img {
    transform: scale(1.05);
  }
  
  /* Enhanced mobile-optimized cards */
  .card-mobile {
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    background: var(--card);
    color: var(--card-foreground);
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .card-mobile:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary);
  }

  /* Enhanced book and bundle cards - Compact version */
  .card-enhanced {
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    background: var(--card);
    color: var(--card-foreground);
    box-shadow: var(--shadow-soft);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    /* Reduced overall height by ~35% */
    min-height: auto;
  }

  /* Compact card spacing optimizations */
  .card-enhanced .card-header {
    padding: 0.375rem 0.375rem 0.25rem 0.375rem;
  }

  @media (min-width: 640px) {
    .card-enhanced .card-header {
      padding: 0.625rem 0.625rem 0.5rem 0.625rem;
    }
  }

  .card-enhanced .card-content {
    padding: 0.25rem 0.375rem;
  }

  @media (min-width: 640px) {
    .card-enhanced .card-content {
      padding: 0.25rem 0.625rem;
    }
  }

  .card-enhanced .card-footer {
    padding: 0.25rem 0.375rem 0.375rem 0.375rem;
  }

  @media (min-width: 640px) {
    .card-enhanced .card-footer {
      padding: 0.25rem 0.625rem 0.5rem 0.625rem;
    }
  }

  .card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-strong);
    border-color: var(--primary);
  }

  .card-enhanced:hover::before {
    opacity: 1;
  }

  /* Mobile-optimized card heights for better screen utilization */
  @media (max-width: 640px) {
    .card-enhanced {
      /* Further height reduction on mobile for maximum visibility */
      font-size: 0.875rem;
    }

    .card-enhanced .aspect-\[3\/4\] {
      /* Slightly reduce aspect ratio on mobile for more content visibility */
      aspect-ratio: 3/4.2;
    }

    .card-enhanced .aspect-\[4\/3\] {
      /* Slightly reduce aspect ratio for bundles on mobile */
      aspect-ratio: 4/3.2;
    }
  }

  /* Ensure buttons remain touch-friendly despite size reduction */
  .card-enhanced button {
    min-height: 1.75rem;
    touch-action: manipulation;
  }

  @media (min-width: 640px) {
    .card-enhanced button {
      min-height: 2rem;
    }
  }
  
  /* Responsive typography scale */
  .heading-responsive-xl {
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 700;
  }
  
  @media (min-width: 640px) {
    .heading-responsive-xl {
      font-size: 1.875rem;
      line-height: 2.25rem;
    }
  }
  
  @media (min-width: 1024px) {
    .heading-responsive-xl {
      font-size: 2.25rem;
      line-height: 2.5rem;
    }
  }
  
  @media (min-width: 1280px) {
    .heading-responsive-xl {
      font-size: 3rem;
      line-height: 1;
    }
  }
  
  .heading-responsive-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
    font-weight: 700;
  }
  
  @media (min-width: 640px) {
    .heading-responsive-lg {
      font-size: 1.5rem;
      line-height: 2rem;
    }
  }
  
  @media (min-width: 1024px) {
    .heading-responsive-lg {
      font-size: 1.875rem;
      line-height: 2.25rem;
    }
  }
  
  .heading-responsive-md {
    font-size: 1.125rem;
    line-height: 1.75rem;
    font-weight: 600;
  }
  
  @media (min-width: 640px) {
    .heading-responsive-md {
      font-size: 1.25rem;
      line-height: 1.75rem;
    }
  }
  
  @media (min-width: 1024px) {
    .heading-responsive-md {
      font-size: 1.5rem;
      line-height: 2rem;
    }
  }
  
  /* Mobile-first button improvements */
  .button-mobile {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  
  .button-mobile:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--ring);
  }
  
  .button-mobile:disabled {
    opacity: 0.5;
    pointer-events: none;
  }
  
  /* Enhanced Visual Effects and Glassmorphism */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: var(--radius-lg);
  }

  .glass-header {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid var(--glass-border);
  }

  .gradient-warm {
    background: var(--gradient-warm);
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: 0 2px 8px oklch(0.25 0.02 45 / 0.08), 0 1px 3px oklch(0.25 0.02 45 / 0.06);
  }

  .shadow-medium {
    box-shadow: 0 4px 16px oklch(0.25 0.02 45 / 0.12), 0 2px 6px oklch(0.25 0.02 45 / 0.08);
  }

  .shadow-strong {
    box-shadow: 0 8px 32px oklch(0.25 0.02 45 / 0.16), 0 4px 12px oklch(0.25 0.02 45 / 0.12);
  }

  /* Subtle animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px oklch(0.25 0.02 45 / 0.15);
  }

  .hover-glow {
    transition: box-shadow 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px oklch(0.45 0.08 45 / 0.3);
  }

  /* Responsive layout utilities */
  .layout-mobile {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .content-mobile {
    flex: 1 1 0%;
    width: 100%;
  }
  
  /* Mobile-optimized modals and sheets */
  .modal-mobile {
    position: fixed;
    inset: 0;
    z-index: 50;
    display: flex;
    align-items: flex-end;
    justify-content: center;
  }
  
  @media (min-width: 640px) {
    .modal-mobile {
      align-items: center;
    }
  }
  
  .sheet-mobile {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    margin-top: 6rem;
    display: flex;
    height: auto;
    flex-direction: column;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-width: 1px;
    background-color: var(--background);
  }
  
  @media (min-width: 640px) {
    .sheet-mobile {
      left: auto;
      right: auto;
      bottom: auto;
      margin-top: 0;
      height: auto;
      max-width: 32rem;
      border-radius: 0.5rem;
    }
  }
}
