{"name": "astewai-bookstore", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "vitest --run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --run --coverage", "test:unit": "vitest --run src/**/*.test.{ts,tsx}", "test:integration": "vitest --run src/**/*.integration.test.{ts,tsx}", "test:e2e": "vitest --run src/test/e2e/**/*.test.{ts,tsx}", "test:performance": "vitest --run src/test/performance/**/*.test.{ts,tsx}", "test:accessibility": "vitest --run src/test/accessibility/**/*.test.{ts,tsx}", "test:components": "vitest --run src/components/**/*.test.{ts,tsx}", "test:api": "vitest --run src/app/api/**/*.test.{ts,tsx}", "test:email-flows": "node scripts/test-email-flows.js", "test:services": "vitest --run src/lib/**/*.test.{ts,tsx}", "admin:check": "node scripts/check-users.js", "admin:make": "node scripts/make-admin.js", "profiles:fix": "node scripts/fix-missing-profiles.js", "profiles:debug": "node scripts/debug-profiles.js", "seo:validate": "node scripts/validate-seo.js", "email:demo": "node scripts/demo-email-system.js", "email:test": "vitest --run src/**/*email*.test.{ts,tsx}", "dev:fix": "node scripts/fix-dev-server.js", "dev:restart": "node scripts/restart-dev.js", "storage:fix": "node scripts/fix-storage.js", "build:safe": "next build", "type-check:safe": "tsc --project tsconfig.build.json --noEmit", "build:deploy": "copy next.config.deploy.js next.config.js && copy src\\app\\sitemap.deploy.ts src\\app\\sitemap.ts && next build", "deploy:vercel": "npm run build:deploy && vercel --prod", "deploy:safe": "npm run build:deploy"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-email/components": "^0.4.0", "@react-email/render": "^1.1.4", "@sentry/nextjs": "^10.1.0", "@sentry/profiling-node": "^10.1.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.1", "@tanstack/react-query": "^5.84.0", "@tanstack/react-query-devtools": "^5.84.0", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.4.4", "plausible-tracker": "^0.3.9", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "resend": "^4.8.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "web-push": "^3.6.7", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@react-email/tailwind": "^1.2.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^3.2.4", "axe-core": "^4.10.3", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "jsdom": "^26.1.0", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5", "vitest": "^3.2.4"}}